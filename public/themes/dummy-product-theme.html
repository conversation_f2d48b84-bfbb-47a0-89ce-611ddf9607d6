<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern E-commerce Store</title>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header Styles */
        .header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px 0;
            position: relative;
        }

        .brand-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .brand-logo {
            position: absolute;
            left: 0;
        }

        .brand-logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            position: absolute;
            left: 0;
        }

        .store-name {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
        }

        /* Hero Section */
        .hero {
            background: #f8f9fa;
            padding: 0;
            position: relative;
            overflow: hidden;
        }

        .hero-slider {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
        }

        .hero-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-slide.active {
            opacity: 1;
        }

        .hero-slide-1 {
            background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
        }

        .hero-slide-2 {
            background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80');
        }

        .hero-slide-3 {
            background-image: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('https://images.unsplash.com/photo-1472851294608-062f824d29cc?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
        }

        .hero-content {
            text-align: center;
            color: white;
            z-index: 2;
            max-width: 600px;
            padding: 0 20px;
        }

        .hero h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .hero p {
            font-size: 20px;
            margin: 0 auto;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .hero-dots {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 3;
        }

        .hero-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .hero-dot.active {
            background: white;
        }

        .hero-image {
            display: none;
        }

        /* Category Carousel */
        .category-section {
            background: white;
            padding: 40px 0;
        }

        .section-title {
            text-align: center;
            font-size: 32px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 40px;
        }

        .category-carousel {
            display: flex;
            gap: 30px;
            justify-content: center;
            flex-wrap: wrap;
            overflow-x: auto;
            padding: 0 20px;
        }

        .category-carousel::-webkit-scrollbar {
            display: none;
        }

        .category-item {
            text-align: center;
            cursor: pointer;
            padding: 20px;
            border-radius: 12px;
            background: #f8f9fa;
            border: 2px solid transparent;
            min-width: 120px;
        }

        .category-item:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        .category-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .category-name {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
        }

        /* Products Section */
        .products-section {
            padding: 60px 0;
            background: #f8f9fa;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .product-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            cursor: pointer;
        }

        .product-card:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .product-image {
            width: 100%;
            height: 200px;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
        }

        .product-1 .product-image { background-image: url('https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'); }
        .product-2 .product-image { background-image: url('https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'); }
        .product-3 .product-image { background-image: url('https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'); }
        .product-4 .product-image { background-image: url('https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'); }
        .product-5 .product-image { background-image: url('https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'); }
        .product-6 .product-image { background-image: url('https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'); }
        .product-7 .product-image { background-image: url('https://images.unsplash.com/photo-1602143407151-7111542de6e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'); }
        .product-8 .product-image { background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'); }

        .product-info {
            padding: 20px;
        }

        .product-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .product-description {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .product-price {
            font-size: 20px;
            font-weight: 700;
            color: #667eea;
        }

        /* Footer */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 50px 0 30px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 30px;
        }

        .footer-section h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #ecf0f1;
        }

        .footer-section p {
            color: #bdc3c7;
            line-height: 1.6;
        }

        .social-icons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            background: #34495e;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-weight: bold;
        }

        .social-icon:hover {
            background: #667eea;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #34495e;
            color: #bdc3c7;
        }

        /* Bottom Menu for Mobile */
        .bottom-menu {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e9ecef;
            display: none;
            z-index: 100;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .bottom-menu-items {
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
        }

        .bottom-menu-item {
            text-align: center;
            color: #6c757d;
            font-size: 10px;
            cursor: pointer;
        }

        .bottom-menu-item.active {
            color: #667eea;
        }

        .bottom-menu-icon {
            font-size: 16px;
            margin-bottom: 3px;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 20px;
        }

        .bottom-menu-icon svg {
            width: 18px;
            height: 18px;
            fill: currentColor;
        }

        /* Whamart Watermark */
        .whamart-watermark {
            position: fixed;
            bottom: 50px; /* Above bottom menu */
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid #e9ecef;
            padding: 8px 20px;
            text-align: center;
            font-size: 11px;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 99;
            display: none;
        }

        .whamart-watermark:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .whamart-watermark .brand-name {
            color: #667eea;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }

            .header-content {
                padding: 10px 0;
            }

            .store-name {
                font-size: 20px;
            }

            .brand-logo {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .hero-slider {
                height: 250px;
            }

            .hero h1 {
                font-size: 24px;
                margin-bottom: 15px;
            }

            .hero p {
                font-size: 14px;
                margin-bottom: 20px;
            }

            .section-title {
                font-size: 20px;
                margin-bottom: 20px;
            }

            .category-carousel {
                gap: 15px;
                justify-content: flex-start;
                flex-wrap: nowrap;
                padding: 0 15px;
            }

            .category-item {
                min-width: 80px;
                padding: 10px;
                flex-shrink: 0;
            }

            .category-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
                margin-bottom: 8px;
            }

            .category-name {
                font-size: 12px;
            }

            .products-grid {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }

            .product-card {
                border-radius: 8px;
            }

            .product-image {
                height: 120px;
                font-size: 12px;
            }

            .product-info {
                padding: 12px;
            }

            .product-name {
                font-size: 14px;
                margin-bottom: 5px;
            }

            .product-description {
                font-size: 12px;
                margin-bottom: 8px;
            }

            .product-price {
                font-size: 16px;
            }

            .footer {
                display: none;
            }

            .bottom-menu {
                display: block;
            }

            .whamart-watermark {
                display: block;
            }

            body {
                padding-bottom: 80px; /* Increased to accommodate watermark + menu */
            }
        }

        @media (min-width: 769px) {
            .bottom-menu {
                display: none !important;
            }
        }

        /* Chat Interface Styles */
        .chat-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(8px);
            z-index: 999;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .chat-backdrop.active {
            display: flex;
        }

        .chat-interface {
            position: relative;
            width: 100%;
            height: 100%;
            background: white;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            border-radius: 0;
            box-shadow: none;
        }

        /* Mobile Styles - Full Screen */
        @media (max-width: 768px) {
            .chat-backdrop {
                padding: 0;
                background: transparent;
                backdrop-filter: none;
            }

            .chat-interface {
                width: 100%;
                height: 100%;
                border-radius: 0;
                box-shadow: none;
            }
        }

        /* Desktop Modal Styles */
        @media (min-width: 769px) {
            .chat-interface {
                width: 450px;
                height: 600px;
                max-height: 80vh;
                border-radius: 16px;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                animation: modalSlideIn 0.3s ease-out;
            }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .chat-header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            z-index: 100;
        }

        .chat-header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px 20px;
            position: relative;
        }

        .chat-back-btn {
            position: absolute;
            left: 20px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #667eea;
            padding: 5px;
        }

        .chat-brand-logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .chat-brand-logo-container {
            position: absolute;
            left: 60px;
            display: inline-block;
        }

        .verified-badge {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            background-image: url('../verified-badge.webp');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .chat-store-info {
            text-align: center;
            flex: 1;
        }

        .chat-store-name {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
        }

        .chat-assistant-info {
            font-size: 12px;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .online-indicator {
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            display: inline-block;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 6px rgba(40, 167, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }

        .chat-disclaimer {
            background: #e8f4fd;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 12px 15px;
            margin: 15px 0;
            text-align: center;
            font-size: 13px;
            color: #2c5282;
            line-height: 1.4;
        }

        .chat-disclaimer .secure-icon {
            color: #667eea;
            margin-right: 5px;
            font-weight: bold;
        }

        .chat-content {
            flex: 1;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .chat-placeholder {
            text-align: center;
            color: #6c757d;
            margin: auto;
        }

        .chat-placeholder h2 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .chat-message {
            margin-bottom: 15px;
            max-width: 80%;
            animation: fadeInUp 0.3s ease;
        }

        .chat-message.bot {
            align-self: flex-start;
        }

        .chat-message.user {
            align-self: flex-end;
        }

        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message-bubble.bot {
            background: #e9ecef;
            color: #2c3e50;
            border-bottom-left-radius: 4px;
        }

        .message-bubble.user {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message-time {
            font-size: 11px;
            color: #6c757d;
            margin-top: 4px;
            text-align: right;
        }

        .bot .message-time {
            text-align: left;
        }

        .typing-indicator {
            align-self: flex-start;
            margin-bottom: 15px;
            animation: fadeInUp 0.3s ease;
        }

        .typing-bubble {
            background: #e9ecef;
            padding: 12px 16px;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #6c757d;
            border-radius: 50%;
            animation: typingDot 1.4s infinite;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes typingDot {
            0%, 60%, 100% {
                opacity: 0.3;
            }
            30% {
                opacity: 1;
            }
        }

        .selected-item-info {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .selected-item-image {
            width: 50px;
            height: 50px;
            background-size: cover;
            background-position: center;
            border-radius: 6px;
            flex-shrink: 0;
        }

        .selected-item-details {
            flex: 1;
        }

        .selected-item-name {
            font-size: 14px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 4px;
            line-height: 1.3;
        }

        .selected-item-price {
            font-size: 16px;
            font-weight: 700;
            color: #667eea;
        }

        .chat-input-container {
            background: white;
            border-top: 1px solid #e9ecef;
            padding: 15px 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 25px;
            padding: 12px 20px;
            font-size: 16px;
            outline: none;
            background: #f8f9fa;
        }

        .chat-input:focus {
            border-color: #667eea;
            background: white;
        }

        .chat-send-btn {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
        }

        .chat-send-btn:hover {
            background: #5a6fd8;
        }

        .chat-send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="brand-logo">S</div>
                <div class="store-name">Modern Store</div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-slider">
            <div class="hero-slide hero-slide-1 active">
                <div class="hero-content">
                    <h1>Welcome to Our Store</h1>
                    <p>Discover amazing products with unbeatable quality and prices</p>
                </div>
            </div>
            <div class="hero-slide hero-slide-2">
                <div class="hero-content">
                    <h1>Premium Quality Products</h1>
                    <p>Shop the latest trends and bestsellers with confidence</p>
                </div>
            </div>
            <div class="hero-slide hero-slide-3">
                <div class="hero-content">
                    <h1>Fast & Secure Shopping</h1>
                    <p>Your satisfaction is our priority with quick delivery</p>
                </div>
            </div>
            <div class="hero-dots">
                <div class="hero-dot active" onclick="currentSlide(1)"></div>
                <div class="hero-dot" onclick="currentSlide(2)"></div>
                <div class="hero-dot" onclick="currentSlide(3)"></div>
            </div>
        </div>
    </section>

    <!-- Category Section -->
    <section class="category-section">
        <div class="container">
            <h2 class="section-title">Shop by Category</h2>
            <div class="category-carousel">
                <div class="category-item">
                    <div class="category-icon">📱</div>
                    <div class="category-name">Electronics</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">👕</div>
                    <div class="category-name">Fashion</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">🏠</div>
                    <div class="category-name">Home & Garden</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">📚</div>
                    <div class="category-name">Books</div>
                </div>
                <div class="category-item">
                    <div class="category-icon">🎮</div>
                    <div class="category-name">Gaming</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <h2 class="section-title">Featured Products</h2>
            <div class="products-grid">
                <div class="product-card product-1">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">Premium Wireless Headphones</div>
                        <div class="product-description">High-quality sound with noise cancellation technology</div>
                        <div class="product-price">₹16,499</div>
                    </div>
                </div>
                <div class="product-card product-2">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">Smart Fitness Watch</div>
                        <div class="product-description">Track your health and fitness goals with style</div>
                        <div class="product-price">₹24,999</div>
                    </div>
                </div>
                <div class="product-card product-3">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">Organic Cotton T-Shirt</div>
                        <div class="product-description">Comfortable and sustainable fashion choice</div>
                        <div class="product-price">₹2,499</div>
                    </div>
                </div>
                <div class="product-card product-4">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">Professional Camera Lens</div>
                        <div class="product-description">Capture stunning photos with crystal clear quality</div>
                        <div class="product-price">₹49,999</div>
                    </div>
                </div>
                <div class="product-card product-5">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">Ergonomic Office Chair</div>
                        <div class="product-description">Comfortable seating for long work sessions</div>
                        <div class="product-price">₹33,299</div>
                    </div>
                </div>
                <div class="product-card product-6">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">Bluetooth Speaker</div>
                        <div class="product-description">Portable speaker with amazing sound quality</div>
                        <div class="product-price">₹7,499</div>
                    </div>
                </div>
                <div class="product-card product-7">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">Stainless Steel Water Bottle</div>
                        <div class="product-description">Keep your drinks at the perfect temperature</div>
                        <div class="product-price">₹2,099</div>
                    </div>
                </div>
                <div class="product-card product-8">
                    <div class="product-image"></div>
                    <div class="product-info">
                        <div class="product-name">LED Desk Lamp</div>
                        <div class="product-description">Adjustable lighting for your workspace</div>
                        <div class="product-price">₹4,199</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>About Us</h3>
                    <p>We are committed to providing high-quality products and exceptional customer service. Your satisfaction is our top priority.</p>
                </div>
                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <p>Email: <EMAIL><br>
                    Phone: +****************<br>
                    Address: 123 Commerce St, City, State 12345</p>
                </div>
                <div class="footer-section">
                    <h3>Follow Us</h3>
                    <p>Stay connected with us on social media</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon">YT</a>
                        <a href="#" class="social-icon">FB</a>
                        <a href="#" class="social-icon">IG</a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Modern Store. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Whamart Watermark for Mobile -->
    <div class="whamart-watermark" id="whamartWatermark">
        This store built on <span class="brand-name">Whamart</span>, create yours now
    </div>

    <!-- Bottom Menu for Mobile -->
    <div class="bottom-menu">
        <div class="bottom-menu-items">
            <div class="bottom-menu-item active" id="homeMenuItem">
                <div class="bottom-menu-icon">
                    <svg viewBox="0 0 24 24">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </div>
                <div>Home</div>
            </div>
            <div class="bottom-menu-item" id="categoryMenuItem">
                <div class="bottom-menu-icon">
                    <svg viewBox="0 0 24 24">
                        <path d="M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm6 6h4v-4h-4v4zm0 6h4v-4h-4v4z"/>
                    </svg>
                </div>
                <div>Category</div>
            </div>
            <div class="bottom-menu-item" id="contactMenuItem">
                <div class="bottom-menu-icon">
                    <svg viewBox="0 0 24 24">
                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                    </svg>
                </div>
                <div>Contact</div>
            </div>
        </div>
    </div>

    <!-- Chat Interface -->
    <div class="chat-backdrop" id="chatBackdrop">
        <div class="chat-interface" id="chatInterface">
        <div class="chat-header">
            <div class="chat-header-content">
                <button class="chat-back-btn" onclick="closeChatInterface()">←</button>
                <div class="chat-brand-logo-container">
                    <div class="chat-brand-logo">S</div>
                    <div class="verified-badge"></div>
                </div>
                <div class="chat-store-info">
                    <div class="chat-store-name">Modern Store</div>
                    <div class="chat-assistant-info">
                        <span class="online-indicator"></span>
                        <span id="chatbotName">Satish</span>
                        <span>(Online)</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="chat-content">
            <div class="chat-messages" id="chatMessages">
                <!-- Security Disclaimer -->
                <div class="chat-disclaimer">
                    <span class="secure-icon">🔒</span>
                    This store uses Whamart's secure service to chat with you. Your data is secure and protected.
                </div>

                <div class="selected-item-info" id="selectedItemInfo" style="display: none;">
                    <div class="selected-item-image" id="selectedItemImage"></div>
                    <div class="selected-item-details">
                        <div class="selected-item-name" id="selectedItemName"></div>
                        <div class="selected-item-price" id="selectedItemPrice"></div>
                    </div>
                </div>

                <div class="chat-placeholder" id="chatPlaceholder">
                    <h2>AI Assistant</h2>
                    <p>Select a product or category to start chatting with our AI salesman.</p>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="chat-input-container">
                <input type="text" class="chat-input" placeholder="Type your message..." id="chatInput">
                <button class="chat-send-btn" onclick="sendMessage()" id="sendBtn">
                    ➤
                </button>
            </div>
        </div>
    </div>
    </div>

    <script>
        // Hero Slider Functionality
        let currentSlideIndex = 1;
        const totalSlides = 3;

        function showSlide(n) {
            const slides = document.querySelectorAll('.hero-slide');
            const dots = document.querySelectorAll('.hero-dot');

            if (n > totalSlides) currentSlideIndex = 1;
            if (n < 1) currentSlideIndex = totalSlides;

            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            slides[currentSlideIndex - 1].classList.add('active');
            dots[currentSlideIndex - 1].classList.add('active');
        }

        function currentSlide(n) {
            currentSlideIndex = n;
            showSlide(currentSlideIndex);
        }

        function nextSlide() {
            currentSlideIndex++;
            showSlide(currentSlideIndex);
        }

        // Auto slide every 5 seconds
        setInterval(nextSlide, 5000);

        // Handle responsive behavior
        function handleResize() {
            const bottomMenu = document.querySelector('.bottom-menu');
            const whamartWatermark = document.querySelector('.whamart-watermark');
            if (window.innerWidth <= 768) {
                bottomMenu.style.display = 'block';
                whamartWatermark.style.display = 'block';
            } else {
                bottomMenu.style.display = 'none';
                whamartWatermark.style.display = 'none';
            }
        }

        // Initial check
        handleResize();

        // Listen for window resize
        window.addEventListener('resize', handleResize);

        // Handle bottom menu item clicks
        document.querySelectorAll('.bottom-menu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.bottom-menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');

                // Handle navigation based on menu item
                const itemId = this.id;
                handleBottomMenuNavigation(itemId);
            });
        });

        // Navigation function for bottom menu
        function handleBottomMenuNavigation(itemId) {
            switch(itemId) {
                case 'homeMenuItem':
                    scrollToSection('hero');
                    break;
                case 'categoryMenuItem':
                    scrollToSection('category-section');
                    break;
                case 'contactMenuItem':
                    openContactChat();
                    break;
            }
        }

        // Smooth scroll to section
        function scrollToSection(sectionClass) {
            const section = document.querySelector('.' + sectionClass);
            if (section) {
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // Open contact chat
        function openContactChat() {
            const contactData = {
                name: 'Contact Support',
                icon: '💬'
            };
            openChatInterface('contact', contactData);
        }

        // Chat Interface Functions
        let currentChatData = null;

        // Store configuration - Store owner can customize these
        const storeConfig = {
            chatbotName: 'Satish',  // Store owner can change this name
            storeName: 'Modern Store'
        };

        // Function to set chatbot name (can be called by store owner)
        function setChatbotName(name) {
            storeConfig.chatbotName = name;
            document.getElementById('chatbotName').textContent = name;
        }

        function openChatInterface(type, data) {
            const chatBackdrop = document.getElementById('chatBackdrop');
            const chatInterface = document.getElementById('chatInterface');
            const selectedItemInfo = document.getElementById('selectedItemInfo');
            const selectedItemImage = document.getElementById('selectedItemImage');
            const selectedItemName = document.getElementById('selectedItemName');
            const selectedItemPrice = document.getElementById('selectedItemPrice');
            const chatPlaceholder = document.getElementById('chatPlaceholder');

            // Store current chat data
            currentChatData = { type, data };

            // Clear previous chat messages
            clearChatMessages();

            if (type === 'product') {
                selectedItemInfo.style.display = 'block';
                selectedItemImage.style.backgroundImage = data.image;
                selectedItemName.textContent = data.name;
                selectedItemPrice.textContent = data.price;
            } else if (type === 'category') {
                selectedItemInfo.style.display = 'block';
                selectedItemImage.style.backgroundImage = 'none';
                selectedItemImage.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                selectedItemImage.innerHTML = '<div style="color: white; font-size: 24px; display: flex; align-items: center; justify-content: center; height: 100%;">' + data.icon + '</div>';
                selectedItemName.textContent = data.name;
                selectedItemPrice.textContent = 'Browse Category';
            } else if (type === 'contact') {
                selectedItemInfo.style.display = 'block';
                selectedItemImage.style.backgroundImage = 'none';
                selectedItemImage.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                selectedItemImage.innerHTML = '<div style="color: white; font-size: 24px; display: flex; align-items: center; justify-content: center; height: 100%;">💬</div>';
                selectedItemName.textContent = data.name;
                selectedItemPrice.textContent = 'Customer Support';
            }

            // Hide placeholder
            chatPlaceholder.style.display = 'none';

            chatBackdrop.classList.add('active');
            document.body.style.overflow = 'hidden';

            // Send welcome message after a short delay
            setTimeout(() => {
                sendWelcomeMessage(type, data);
            }, 500);
        }

        function clearChatMessages() {
            const chatMessages = document.getElementById('chatMessages');
            const messagesToRemove = chatMessages.querySelectorAll('.chat-message, .typing-indicator');
            messagesToRemove.forEach(message => message.remove());
        }

        function sendWelcomeMessage(type, data) {
            const chatbotName = document.getElementById('chatbotName').textContent;

            if (type === 'product') {
                const welcomeMessage = `नमस्ते! मैं ${chatbotName} हूँ, Modern Store से। 🙏 आपका स्वागत है! मैं देख रहा हूँ कि आप ${data.name} में interested हैं। क्या आप इसके बारे में और जानना चाहेंगे? मैं आपकी पूरी सहायता करूंगा।`;
                addBotMessage(welcomeMessage);
            } else if (type === 'category') {
                const welcomeMessage = `नमस्ते! मैं ${chatbotName} हूँ, Modern Store से। 🙏 आपका स्वागत है! आपने ${data.name} category select की है। मैं इस category के best products के बारे में आपको बता सकता हूँ। कैसे मदद कर सकता हूँ?`;
                addBotMessage(welcomeMessage);
            } else if (type === 'contact') {
                const welcomeMessage = `नमस्ते! मैं ${chatbotName} हूँ, Modern Store से। 🙏 आपका स्वागत है! मैं यहाँ आपकी हर तरह की सहायता के लिए उपलब्ध हूँ। आप मुझसे products, orders, delivery या कुछ भी पूछ सकते हैं। बताइए, कैसे मदद कर सकता हूँ?`;
                addBotMessage(welcomeMessage);
            }
        }

        function addBotMessage(message) {
            showTypingIndicator();

            setTimeout(() => {
                hideTypingIndicator();
                const chatMessages = document.getElementById('chatMessages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'chat-message bot';

                const currentTime = new Date().toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                messageDiv.innerHTML = `
                    <div class="message-bubble bot">${message}</div>
                    <div class="message-time">${currentTime}</div>
                `;

                chatMessages.appendChild(messageDiv);
                scrollToBottom();
            }, 1500);
        }

        function addUserMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message user';

            const currentTime = new Date().toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageDiv.innerHTML = `
                <div class="message-bubble user">${message}</div>
                <div class="message-time">${currentTime}</div>
            `;

            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function showTypingIndicator() {
            // Remove any existing typing indicator
            hideTypingIndicator();

            const chatMessages = document.getElementById('chatMessages');
            const typingIndicator = document.createElement('div');
            typingIndicator.className = 'typing-indicator';
            typingIndicator.id = 'typingIndicator';

            typingIndicator.innerHTML = `
                <div class="typing-bubble">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;

            chatMessages.appendChild(typingIndicator);
            scrollToBottom();
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function closeChatInterface() {
            const chatBackdrop = document.getElementById('chatBackdrop');
            const chatPlaceholder = document.getElementById('chatPlaceholder');
            const selectedItemInfo = document.getElementById('selectedItemInfo');

            chatBackdrop.classList.remove('active');
            document.body.style.overflow = 'auto';

            // Reset chat state
            clearChatMessages();
            chatPlaceholder.style.display = 'block';
            selectedItemInfo.style.display = 'none';
            currentChatData = null;
        }

        // Add click events to products
        document.querySelectorAll('.product-card').forEach((product, index) => {
            product.addEventListener('click', function() {
                const productData = {
                    name: this.querySelector('.product-name').textContent,
                    price: this.querySelector('.product-price').textContent,
                    image: window.getComputedStyle(this.querySelector('.product-image')).backgroundImage
                };
                openChatInterface('product', productData);
            });
        });

        // Add click events to categories
        document.querySelectorAll('.category-item').forEach(category => {
            category.addEventListener('click', function() {
                const categoryData = {
                    name: this.querySelector('.category-name').textContent,
                    icon: this.querySelector('.category-icon').textContent
                };
                openChatInterface('category', categoryData);
            });
        });

        // Chat Input Functions
        function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();

            if (message) {
                // Add user message to chat
                addUserMessage(message);

                // Clear input
                chatInput.value = '';

                // Generate AI response
                setTimeout(() => {
                    generateAIResponse(message);
                }, 1000);
            }
        }

        function generateAIResponse(userMessage) {
            let response = '';
            const chatbotName = document.getElementById('chatbotName').textContent;

            if (currentChatData && currentChatData.type === 'product') {
                const productName = currentChatData.data.name;
                const productPrice = currentChatData.data.price;

                // Simple AI responses based on user message
                if (userMessage.toLowerCase().includes('price') || userMessage.toLowerCase().includes('कीमत') || userMessage.toLowerCase().includes('दाम')) {
                    response = `जी हाँ, ${productName} की कीमत ${productPrice} है। मैं आपको बता दूं कि यह एक बेहतरीन product है और इसकी quality excellent है। क्या आप इसे खरीदना चाहेंगे? मैं order process में आपकी मदद कर सकता हूँ।`;
                } else if (userMessage.toLowerCase().includes('feature') || userMessage.toLowerCase().includes('विशेषता') || userMessage.toLowerCase().includes('फीचर')) {
                    response = `${productName} में कई शानदार features हैं। मैं आपको बताता हूँ - यह high quality material से बना है और बहुत long lasting है। क्या आप इसकी detailed specifications जानना चाहते हैं? मैं पूरी जानकारी दे सकता हूँ।`;
                } else if (userMessage.toLowerCase().includes('buy') || userMessage.toLowerCase().includes('खरीद') || userMessage.toLowerCase().includes('order')) {
                    response = `वाह! बहुत बढ़िया choice है। ${productName} really एक excellent product है। मैं अभी आपके order की process शुरू करता हूँ। कृपया अपना contact number share करें, मैं सब arrange कर दूंगा।`;
                } else {
                    response = `${productName} के बारे में और कुछ जानना चाहते हैं? मैं आपको इसकी price (${productPrice}), features, availability - सब कुछ detail में बता सकता हूँ। बताइए कैसे मदद कर सकता हूँ?`;
                }
            } else if (currentChatData && currentChatData.type === 'category') {
                const categoryName = currentChatData.data.name;
                response = `${categoryName} category में हमारे पास कई बेहतरीन products हैं। मैं आपको personally recommend कर सकता हूँ। आप किस specific product के बारे में जानना चाहते हैं? मैं best options suggest करूंगा।`;
            } else if (currentChatData && currentChatData.type === 'contact') {
                // Contact support responses
                if (userMessage.toLowerCase().includes('order') || userMessage.toLowerCase().includes('ऑर्डर')) {
                    response = 'जी बिल्कुल! आपके order के बारे में जानकारी के लिए कृपया अपना order number share करें। मैं तुरंत check करके आपकी order status बता दूंगा।';
                } else if (userMessage.toLowerCase().includes('delivery') || userMessage.toLowerCase().includes('डिलीवरी')) {
                    response = 'हमारी delivery service बहुत fast और reliable है। आमतौर पर 2-3 दिन में delivery हो जाती है। आप कौन से area में हैं? मैं exact delivery time बता सकता हूँ।';
                } else if (userMessage.toLowerCase().includes('return') || userMessage.toLowerCase().includes('रिटर्न')) {
                    response = 'कोई बात नहीं! हमारी 7-day return policy है। अगर आप product से satisfied नहीं हैं तो मैं return process करा दूंगा। कृपया order details share करें।';
                } else if (userMessage.toLowerCase().includes('payment') || userMessage.toLowerCase().includes('पेमेंट')) {
                    response = 'हम सभी popular payment methods accept करते हैं - UPI, Card, Net Banking, Cash on Delivery। आप कौन सा payment method prefer करते हैं? मैं सब arrange कर दूंगा।';
                } else {
                    response = 'मैं यहाँ आपकी हर query में help करने के लिए हूँ। आप order status, delivery, return, payment या किसी भी product के बारे में पूछ सकते हैं। मैं personally handle करूंगा।';
                }
            } else {
                response = 'मैं आपकी मदद करने के लिए यहाँ हूँ। कृपया बताएं कि आप किस product के बारे में जानना चाहते हैं? मैं पूरी जानकारी दूंगा।';
            }

            addBotMessage(response);
        }

        // Handle Enter key in chat input
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Enable/disable send button based on input
        document.getElementById('chatInput').addEventListener('input', function() {
            const sendBtn = document.getElementById('sendBtn');
            if (this.value.trim()) {
                sendBtn.disabled = false;
            } else {
                sendBtn.disabled = true;
            }
        });

        // Close modal when clicking on backdrop
        document.getElementById('chatBackdrop').addEventListener('click', function(e) {
            if (e.target === this) {
                closeChatInterface();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const chatBackdrop = document.getElementById('chatBackdrop');
                if (chatBackdrop.classList.contains('active')) {
                    closeChatInterface();
                }
            }
        });

        // Whamart watermark click handler
        document.getElementById('whamartWatermark').addEventListener('click', function() {
            window.open('https://whamart.shop', '_blank');
        });
    </script>
</body>
</html>
